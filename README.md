# Project Management System

A Windows desktop GUI application built with PySide6 (Qt) for managing projects, users, and storage vaults with check-in/check-out capabilities.

## Features

- **User Management**: Add, edit, delete users with admin privileges and authentication
- **Login System**: Secure authentication with password hashing
- **Vault Management**: Create and manage storage locations for projects
- **Project Management**: Add projects with folder/file selection and vault assignment
- **Check-in/Check-out**: Template system for future version control development
- **Modern UI**: Clean, Windows-compliant interface following Microsoft design guidelines
- **SQLite Database**: Lightweight, embedded database for data persistence
- **Logging**: Comprehensive application logging for debugging and monitoring

## Screenshots

The application features a modern toolbar interface with the following buttons:
- 👥 User Management (Admin only)
- 🔐 Login/Logout
- 🗄️ Vault Management
- 📁 Add Project
- 📥 Check In (Template)
- 📤 Check Out (Template)

## Requirements

- Windows 10/11
- Python 3.12
- Anaconda or Miniconda
- PySide6 (Qt6)

## Installation

### Automated Installation (Recommended)

1. Clone or download this repository
2. Run `install.bat` as Administrator
3. The script will:
   - Create a conda environment with Python 3.12
   - Install all required dependencies
   - Set up the application

### Manual Installation

1. Create conda environment:
   ```bash
   conda create -n project_management python=3.12
   conda activate project_management
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Running the Application

### Using Batch File (Recommended)
```bash
run.bat
```

### Manual Execution
```bash
conda activate project_management
python main.py
```

## Default Login

- **Username**: admin
- **Password**: admin123

## Project Structure

```
ProjectManagement/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── install.bat            # Automated installation script
├── run.bat                # Application launcher
├── README.md              # This file
├── ui/                    # User interface modules
│   ├── __init__.py
│   ├── main_window.py     # Main application window
│   ├── login_dialog.py    # Login dialog
│   ├── user_management_dialog.py    # User management
│   ├── vault_management_dialog.py   # Vault management
│   └── add_project_dialog.py        # Project creation
├── logic/                 # Business logic modules
│   ├── database.py        # Database operations
│   └── config.py          # Configuration management
├── assets/                # Application assets (icons, etc.)
├── logs/                  # Application log files
└── data/                  # Database and configuration files
```

## Usage

### First Time Setup

1. Run the application using `run.bat`
2. Login with default credentials (admin/admin123)
3. Create vaults for storing projects
4. Add users if needed (Admin only)
5. Create projects and assign them to vaults

### User Management (Admin Only)

- Add new users with username, full name, email
- Set admin privileges
- Edit user information
- Change passwords
- Delete users (soft delete)

### Vault Management

- Create storage vaults with name and file system path
- Edit vault information
- Delete vaults
- Open vault folders in Windows Explorer

### Project Management

- Create projects by selecting folders or files
- Assign projects to vaults
- Set user group permissions (template)
- View project information

### Check-in/Check-out (Template)

The check-in and check-out buttons are templates for future development and currently show information dialogs explaining the intended functionality.

## Database

The application uses SQLite for data storage with the following tables:
- `users` - User accounts and authentication
- `vaults` - Storage locations
- `projects` - Project information
- `user_groups` - User group definitions (for future use)
- `user_group_members` - Group membership (for future use)
- `project_permissions` - Project access permissions (for future use)

## Configuration

Application settings are stored in `data/config.json` and include:
- Window size and position
- UI preferences
- Database settings
- Security settings

## Logging

Application logs are stored in the `logs/` directory:
- `app.log` - Main application log with timestamps
- Console output for real-time monitoring

## Development Notes

### Code Style
- Follows PEP8 style guidelines
- Comprehensive docstrings for all classes and methods
- Type hints for better code maintainability

### Architecture
- Separation of UI and business logic
- Database abstraction layer
- Configuration management
- Error handling and logging

### Threading
- UI operations run on main thread
- Database operations are designed to be thread-safe
- Future background tasks can be implemented using QThread

## Future Enhancements

- User group management implementation
- Full check-in/check-out version control system
- File conflict resolution
- Network storage support
- Advanced permission management
- Backup and restore functionality
- System tray integration
- Windows notifications

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Ensure conda environment is activated
   - Check that PySide6 is installed: `pip list | findstr PySide6`

2. **Database errors**
   - Check that the `data/` directory is writable
   - Delete `data/project_management.db` to reset database

3. **Permission errors**
   - Run as Administrator if needed
   - Check file system permissions for application directory

### Getting Help

Check the application logs in the `logs/` directory for detailed error information.

## License

This project is provided as-is for educational and development purposes.

## Author

Created by Augment Agent using PySide6 and modern Windows development practices.