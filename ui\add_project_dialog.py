"""
Add project dialog for Project Management Application.

This module provides project creation functionality including
folder selection, vault assignment, and user group permissions.

Author: Augment Agent
Date: 2025-06-22
"""

import logging
import os
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QPushButton, QMessageBox, QFrame, QLabel, QTextEdit,
    QComboBox, QFileDialog, QCheckBox, QListWidget, QListWidgetItem
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from logic.database import DatabaseManager


class AddProjectDialog(QDialog):
    """Dialog for adding new projects."""

    def __init__(self, db_manager: DatabaseManager, current_user: dict, parent=None):
        """
        Initialize the add project dialog.

        Args:
            db_manager: Database manager instance
            current_user: Current logged-in user
            parent: Parent widget
        """
        super().__init__(parent)

        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.current_user = current_user

        self.setup_ui()
        self.setup_connections()
        self.load_vaults()

    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("Add New Project")
        self.setFixedSize(600, 500)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header
        header_label = QLabel("Add New Project")
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        main_layout.addWidget(header_label)

        # Info label
        info_label = QLabel("Create a new project by selecting a folder/file and assigning it to a vault with user group permissions.")
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # Form
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setContentsMargins(15, 15, 15, 15)
        form_layout.setSpacing(12)

        # Project Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter project name")
        form_layout.addRow("Project Name:", self.name_edit)

        # Project Path selection
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("Select project folder or file")
        self.path_edit.setReadOnly(True)

        self.browse_folder_button = QPushButton("Browse Folder")
        self.browse_folder_button.setMaximumWidth(100)
        self.browse_folder_button.setStyleSheet(self._get_button_style("#007bff"))

        self.browse_file_button = QPushButton("Browse File")
        self.browse_file_button.setMaximumWidth(100)
        self.browse_file_button.setStyleSheet(self._get_button_style("#17a2b8"))

        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_folder_button)
        path_layout.addWidget(self.browse_file_button)
        form_layout.addRow("Project Path:", path_layout)

        # Vault Selection
        self.vault_combo = QComboBox()
        self.vault_combo.setMinimumHeight(30)
        form_layout.addRow("Vault:", self.vault_combo)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Enter project description (optional)")
        self.description_edit.setMaximumHeight(80)
        form_layout.addRow("Description:", self.description_edit)

        main_layout.addWidget(form_frame)

        # User Group Permissions (simplified for this template)
        permissions_frame = QFrame()
        permissions_frame.setFrameStyle(QFrame.StyledPanel)
        permissions_layout = QVBoxLayout(permissions_frame)
        permissions_layout.setContentsMargins(15, 15, 15, 15)

        permissions_label = QLabel("User Group Permissions (Template)")
        permissions_font = QFont()
        permissions_font.setBold(True)
        permissions_label.setFont(permissions_font)
        permissions_layout.addWidget(permissions_label)

        # Checkboxes for permissions
        self.read_permission = QCheckBox("Allow Read Access")
        self.read_permission.setChecked(True)
        self.read_permission.setEnabled(False)  # Always enabled
        permissions_layout.addWidget(self.read_permission)

        self.checkout_permission = QCheckBox("Allow Check-out")
        permissions_layout.addWidget(self.checkout_permission)

        self.checkin_permission = QCheckBox("Allow Check-in")
        permissions_layout.addWidget(self.checkin_permission)

        note_label = QLabel("Note: User group management will be implemented in future versions.")
        note_label.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        permissions_layout.addWidget(note_label)

        main_layout.addWidget(permissions_frame)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.create_button = QPushButton("Create Project")
        self.create_button.setMinimumHeight(40)
        self.create_button.setStyleSheet(self._get_button_style("#28a745"))

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setStyleSheet(self._get_button_style("#6c757d"))

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.create_button)

        main_layout.addLayout(button_layout)

    def _get_button_style(self, bg_color: str, text_color: str = "#fff") -> str:
        """Get button stylesheet."""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {bg_color}dd;
            }}
            QPushButton:pressed {{
                background-color: {bg_color}bb;
            }}
        """

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.browse_folder_button.clicked.connect(self.browse_folder)
        self.browse_file_button.clicked.connect(self.browse_file)
        self.create_button.clicked.connect(self.create_project)
        self.cancel_button.clicked.connect(self.reject)

        # Auto-fill project name when path is selected
        self.path_edit.textChanged.connect(self.auto_fill_name)

    def load_vaults(self):
        """Load available vaults into the combo box."""
        try:
            vaults = self.db_manager.get_all_vaults()

            self.vault_combo.clear()
            if not vaults:
                self.vault_combo.addItem("No vaults available", None)
                self.vault_combo.setEnabled(False)
                return

            for vault in vaults:
                self.vault_combo.addItem(f"{vault['name']} ({vault['path']})", vault['id'])

        except Exception as e:
            self.logger.error(f"Failed to load vaults: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load vaults:\n{str(e)}")

    def browse_folder(self):
        """Browse for project folder."""
        current_path = self.path_edit.text() or os.path.expanduser("~")

        path = QFileDialog.getExistingDirectory(
            self, "Select Project Folder", current_path
        )

        if path:
            self.path_edit.setText(path)

    def browse_file(self):
        """Browse for project file."""
        current_path = self.path_edit.text() or os.path.expanduser("~")

        path, _ = QFileDialog.getOpenFileName(
            self, "Select Project File", current_path,
            "All Files (*.*)"
        )

        if path:
            self.path_edit.setText(path)

    def auto_fill_name(self):
        """Auto-fill project name based on selected path."""
        path = self.path_edit.text()
        if path and not self.name_edit.text():
            # Extract name from path
            name = os.path.basename(path)
            if name:
                self.name_edit.setText(name)

    def create_project(self):
        """Create the new project."""
        name = self.name_edit.text().strip()
        path = self.path_edit.text().strip()
        description = self.description_edit.toPlainText().strip()

        # Get selected vault
        vault_id = self.vault_combo.currentData()

        # Validate input
        if not name:
            QMessageBox.warning(self, "Validation Error", "Project name is required.")
            return

        if not path:
            QMessageBox.warning(self, "Validation Error", "Project path is required.")
            return

        if vault_id is None:
            QMessageBox.warning(self, "Validation Error", "Please select a vault.")
            return

        # Validate path exists
        if not os.path.exists(path):
            QMessageBox.warning(self, "Path Not Found", f"The path '{path}' does not exist.")
            return

        try:
            # Create the project
            success = self.db_manager.create_project(
                name=name,
                path=path,
                vault_id=vault_id,
                description=description,
                created_by=self.current_user['id']
            )

            if success:
                # Show success message with permission info
                permission_info = []
                if self.read_permission.isChecked():
                    permission_info.append("Read Access")
                if self.checkout_permission.isChecked():
                    permission_info.append("Check-out")
                if self.checkin_permission.isChecked():
                    permission_info.append("Check-in")

                permissions_text = ", ".join(permission_info) if permission_info else "None"

                QMessageBox.information(
                    self, "Success",
                    f"Project '{name}' created successfully!\n\n"
                    f"Path: {path}\n"
                    f"Vault: {self.vault_combo.currentText()}\n"
                    f"Permissions: {permissions_text}\n\n"
                    "Note: User group permissions are templates for future development."
                )
                self.accept()
            else:
                QMessageBox.warning(
                    self, "Error",
                    "Failed to create project. Project name may already exist."
                )

        except Exception as e:
            self.logger.error(f"Failed to create project: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create project:\n{str(e)}")