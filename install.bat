@echo off
REM Project Management System - Automated Installation Script
REM This script sets up the conda environment and installs dependencies

echo ========================================
echo Project Management System Installation
echo ========================================
echo.

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Conda is not installed or not in PATH
    echo Please install Anaconda or Miniconda first
    echo Download from: https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)

echo Conda found. Proceeding with installation...
echo.

REM Create conda environment with Python 3.12
echo Creating conda environment 'project_management' with Python 3.12...
conda create -n project_management python=3.12 -y

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create conda environment
    pause
    exit /b 1
)

echo.
echo Environment created successfully!
echo.

REM Activate environment and install dependencies
echo Activating environment and installing dependencies...
call conda activate project_management

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to activate conda environment
    pause
    exit /b 1
)

REM Install PySide6
echo Installing PySide6...
pip install -r requirements.txt

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To run the application:
echo 1. Open Command Prompt or Anaconda Prompt
echo 2. Run: conda activate project_management
echo 3. Navigate to this directory
echo 4. Run: python main.py
echo.
echo Or use the provided run.bat file
echo.
pause