"""
Main window for Project Management Application.

This module contains the main application window with toolbar buttons
and central content area.

Author: Augment Agent
Date: 2025-06-22
"""

import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QStatusBar, QMenuBar, QMenu, QAction, QMessageBox,
    QSplitter, QTextEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap

from logic.database import DatabaseManager
from logic.config import Config


class MainWindow(QMainWindow):
    """Main application window."""

    # Signals
    user_logged_in = Signal(dict)  # Emitted when user logs in
    user_logged_out = Signal()     # Emitted when user logs out

    def __init__(self):
        """Initialize the main window."""
        super().__init__()

        self.logger = logging.getLogger(__name__)
        self.db_manager = DatabaseManager()
        self.config = Config()
        self.current_user = None

        self.setup_ui()
        self.setup_connections()
        self.setup_status_bar()

        # Load window settings
        self.load_window_settings()

        self.logger.info("Main window initialized")

    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("Project Management System")
        self.setMinimumSize(800, 600)

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create toolbar
        toolbar_frame = self.create_toolbar()
        main_layout.addWidget(toolbar_frame)

        # Create content area
        content_splitter = self.create_content_area()
        main_layout.addWidget(content_splitter)

        # Setup menu bar
        self.setup_menu_bar()

    def create_toolbar(self) -> QFrame:
        """Create the main toolbar with action buttons."""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(80)

        layout = QHBoxLayout(toolbar_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Create toolbar buttons
        self.user_mgmt_btn = self.create_toolbar_button(
            "User Management", "👥", "Manage application users"
        )
        self.login_btn = self.create_toolbar_button(
            "Login", "🔐", "Login to the system"
        )
        self.vault_mgmt_btn = self.create_toolbar_button(
            "Vault Management", "🗄️", "Manage storage vaults"
        )
        self.add_project_btn = self.create_toolbar_button(
            "Add Project", "📁", "Add a new project"
        )
        self.checkin_btn = self.create_toolbar_button(
            "Check In", "📥", "Check in files (template)"
        )
        self.checkout_btn = self.create_toolbar_button(
            "Check Out", "📤", "Check out files (template)"
        )

        # Add buttons to layout
        layout.addWidget(self.user_mgmt_btn)
        layout.addWidget(self.login_btn)
        layout.addWidget(self.vault_mgmt_btn)
        layout.addWidget(self.add_project_btn)
        layout.addWidget(self.checkin_btn)
        layout.addWidget(self.checkout_btn)

        # Add stretch to push buttons to the left
        layout.addStretch()

        # Add current user label
        self.current_user_label = QLabel("Not logged in")
        self.current_user_label.setStyleSheet("font-weight: bold; color: #666;")
        layout.addWidget(self.current_user_label)

        return toolbar_frame

    def create_toolbar_button(self, text: str, icon_text: str, tooltip: str) -> QPushButton:
        """
        Create a toolbar button with consistent styling.

        Args:
            text: Button text
            icon_text: Emoji or text to use as icon
            tooltip: Button tooltip

        Returns:
            Configured QPushButton
        """
        button = QPushButton(f"{icon_text}\n{text}")
        button.setMinimumSize(120, 60)
        button.setMaximumSize(120, 60)
        button.setToolTip(tooltip)
        button.setStyleSheet("""
            QPushButton {
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: #f8f9fa;
                font-size: 10px;
                font-weight: bold;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QPushButton:disabled {
                background-color: #f8f9fa;
                color: #6c757d;
                border-color: #dee2e6;
            }
        """)
        return button

    def create_content_area(self) -> QSplitter:
        """Create the main content area."""
        splitter = QSplitter(Qt.Horizontal)

        # Left panel - Project/Vault list
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_panel.setMinimumWidth(300)
        left_panel.setMaximumWidth(400)

        left_layout = QVBoxLayout(left_panel)
        left_layout.addWidget(QLabel("Projects & Vaults"))

        self.project_table = QTableWidget()
        self.project_table.setColumnCount(3)
        self.project_table.setHorizontalHeaderLabels(["Name", "Type", "Path"])
        self.project_table.horizontalHeader().setStretchLastSection(True)
        left_layout.addWidget(self.project_table)

        # Right panel - Details/Log
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)

        right_layout = QVBoxLayout(right_panel)
        right_layout.addWidget(QLabel("Activity Log"))

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        right_layout.addWidget(self.log_text)

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([300, 500])

        return splitter

    def setup_menu_bar(self):
        """Setup the application menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        settings_action = QAction("&Settings", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_connections(self):
        """Setup signal-slot connections."""
        # Connect toolbar buttons
        self.user_mgmt_btn.clicked.connect(self.show_user_management)
        self.login_btn.clicked.connect(self.show_login)
        self.vault_mgmt_btn.clicked.connect(self.show_vault_management)
        self.add_project_btn.clicked.connect(self.show_add_project)
        self.checkin_btn.clicked.connect(self.show_checkin)
        self.checkout_btn.clicked.connect(self.show_checkout)

        # Connect signals
        self.user_logged_in.connect(self.on_user_logged_in)
        self.user_logged_out.connect(self.on_user_logged_out)

    def setup_status_bar(self):
        """Setup the status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def load_window_settings(self):
        """Load window size and position from configuration."""
        width = self.config.get("app.window_width", 1200)
        height = self.config.get("app.window_height", 800)
        self.resize(width, height)

    def save_window_settings(self):
        """Save current window size and position to configuration."""
        self.config.set("app.window_width", self.width())
        self.config.set("app.window_height", self.height())
        self.config.save()

    def update_ui_state(self):
        """Update UI state based on current user login status."""
        is_logged_in = self.current_user is not None
        is_admin = is_logged_in and self.current_user.get('is_admin', False)

        # Enable/disable buttons based on login status
        self.user_mgmt_btn.setEnabled(is_admin)
        self.vault_mgmt_btn.setEnabled(is_logged_in)
        self.add_project_btn.setEnabled(is_logged_in)
        self.checkin_btn.setEnabled(is_logged_in)
        self.checkout_btn.setEnabled(is_logged_in)

        # Update login button text
        if is_logged_in:
            self.login_btn.setText("🔓\nLogout")
            self.login_btn.setToolTip("Logout from the system")
        else:
            self.login_btn.setText("🔐\nLogin")
            self.login_btn.setToolTip("Login to the system")

    def log_message(self, message: str):
        """Add a message to the activity log."""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

    # Event handlers
    def on_user_logged_in(self, user: dict):
        """Handle user login event."""
        self.current_user = user
        username = user.get('username', 'Unknown')
        full_name = user.get('full_name', username)
        self.current_user_label.setText(f"Logged in as: {full_name}")
        self.current_user_label.setStyleSheet("font-weight: bold; color: #28a745;")
        self.update_ui_state()
        self.log_message(f"User '{username}' logged in successfully")
        self.status_bar.showMessage(f"Welcome, {full_name}!")

    def on_user_logged_out(self):
        """Handle user logout event."""
        if self.current_user:
            username = self.current_user.get('username', 'Unknown')
            self.log_message(f"User '{username}' logged out")

        self.current_user = None
        self.current_user_label.setText("Not logged in")
        self.current_user_label.setStyleSheet("font-weight: bold; color: #666;")
        self.update_ui_state()
        self.status_bar.showMessage("Logged out")

    # Button click handlers (will be implemented with popup dialogs)
    def show_user_management(self):
        """Show user management dialog."""
        try:
            from ui.user_management_dialog import UserManagementDialog
            dialog = UserManagementDialog(self.db_manager, self)
            dialog.exec()
        except ImportError:
            QMessageBox.information(self, "Info", "User Management dialog not yet implemented")

    def show_login(self):
        """Show login dialog or logout."""
        if self.current_user:
            # Logout
            reply = QMessageBox.question(
                self, "Logout", "Are you sure you want to logout?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.user_logged_out.emit()
        else:
            # Login
            try:
                from ui.login_dialog import LoginDialog
                dialog = LoginDialog(self.db_manager, self)
                if dialog.exec() == dialog.Accepted:
                    user = dialog.get_authenticated_user()
                    if user:
                        self.user_logged_in.emit(user)
            except ImportError:
                QMessageBox.information(self, "Info", "Login dialog not yet implemented")

    def show_vault_management(self):
        """Show vault management dialog."""
        try:
            from ui.vault_management_dialog import VaultManagementDialog
            dialog = VaultManagementDialog(self.db_manager, self)
            dialog.exec()
        except ImportError:
            QMessageBox.information(self, "Info", "Vault Management dialog not yet implemented")

    def show_add_project(self):
        """Show add project dialog."""
        try:
            from ui.add_project_dialog import AddProjectDialog
            dialog = AddProjectDialog(self.db_manager, self.current_user, self)
            dialog.exec()
        except ImportError:
            QMessageBox.information(self, "Info", "Add Project dialog not yet implemented")

    def show_checkin(self):
        """Show check-in dialog (template)."""
        QMessageBox.information(
            self, "Check In",
            "Check-in functionality is a template for future development.\n\n"
            "This would typically include:\n"
            "- File selection\n"
            "- Version control integration\n"
            "- Conflict resolution\n"
            "- Metadata management"
        )

    def show_checkout(self):
        """Show check-out dialog (template)."""
        QMessageBox.information(
            self, "Check Out",
            "Check-out functionality is a template for future development.\n\n"
            "This would typically include:\n"
            "- File locking\n"
            "- Version selection\n"
            "- Local workspace management\n"
            "- Permission validation"
        )

    def show_settings(self):
        """Show application settings dialog."""
        QMessageBox.information(self, "Settings", "Settings dialog not yet implemented")

    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self, "About Project Management System",
            "<h3>Project Management System v1.0.0</h3>"
            "<p>A desktop application for managing projects, users, and vaults.</p>"
            "<p><b>Features:</b></p>"
            "<ul>"
            "<li>User management and authentication</li>"
            "<li>Project creation and management</li>"
            "<li>Vault management for storage locations</li>"
            "<li>Check-in/Check-out system (template)</li>"
            "</ul>"
            "<p><b>Built with:</b> PySide6, SQLite</p>"
            "<p><b>Author:</b> Augment Agent</p>"
        )

    def closeEvent(self, event):
        """Handle window close event."""
        # Save window settings
        self.save_window_settings()

        # Log application shutdown
        self.log_message("Application shutting down")
        self.logger.info("Application shutting down")

        event.accept()