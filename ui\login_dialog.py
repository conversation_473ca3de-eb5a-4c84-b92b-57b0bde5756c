"""
Login dialog for Project Management Application.

This module provides user authentication functionality.

Author: Augment Agent
Date: 2025-06-22
"""

import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QPushButton, QLabel, QMessageBox, QCheckBox, QFrame
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap

from logic.database import DatabaseManager


class LoginDialog(QDialog):
    """Login dialog for user authentication."""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        """
        Initialize the login dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager
        self.authenticated_user = None

        self.setup_ui()
        self.setup_connections()

        # Set focus to username field
        self.username_edit.setFocus()

    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("Login - Project Management System")
        self.setFixedSize(400, 300)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Header
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)

        title_label = QLabel("Project Management System")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        subtitle_label = QLabel("Please enter your credentials")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        header_layout.addWidget(subtitle_label)

        main_layout.addWidget(header_frame)

        # Login form
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)

        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter username")
        self.username_edit.setMinimumHeight(35)
        form_layout.addRow("Username:", self.username_edit)

        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Enter password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(35)
        form_layout.addRow("Password:", self.password_edit)

        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember username")
        form_layout.addRow("", self.remember_checkbox)

        main_layout.addWidget(form_frame)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.login_button = QPushButton("Login")
        self.login_button.setMinimumHeight(40)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
            QPushButton:pressed {
                background-color: #3d4142;
            }
        """)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)

        main_layout.addLayout(button_layout)

        # Default credentials info
        info_label = QLabel("Default: admin / admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #28a745; font-size: 10px; font-style: italic;")
        main_layout.addWidget(info_label)

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)

        # Allow Enter key to trigger login
        self.username_edit.returnPressed.connect(self.handle_login)
        self.password_edit.returnPressed.connect(self.handle_login)

    def handle_login(self):
        """Handle login button click."""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()

        # Validate input
        if not username:
            QMessageBox.warning(self, "Login Error", "Please enter a username.")
            self.username_edit.setFocus()
            return

        if not password:
            QMessageBox.warning(self, "Login Error", "Please enter a password.")
            self.password_edit.setFocus()
            return

        # Disable login button during authentication
        self.login_button.setEnabled(False)
        self.login_button.setText("Authenticating...")

        try:
            # Authenticate user
            user = self.db_manager.authenticate_user(username, password)

            if user:
                self.authenticated_user = user
                self.logger.info(f"User '{username}' authenticated successfully")

                # Save username if remember is checked
                if self.remember_checkbox.isChecked():
                    # This could be saved to config in a real application
                    pass

                self.accept()
            else:
                QMessageBox.warning(
                    self, "Login Failed",
                    "Invalid username or password.\n\nPlease check your credentials and try again."
                )
                self.password_edit.clear()
                self.password_edit.setFocus()

        except Exception as e:
            self.logger.error(f"Login error: {e}")
            QMessageBox.critical(
                self, "Login Error",
                f"An error occurred during login:\n{str(e)}"
            )

        finally:
            # Re-enable login button
            self.login_button.setEnabled(True)
            self.login_button.setText("Login")

    def get_authenticated_user(self):
        """
        Get the authenticated user information.

        Returns:
            User dictionary if authentication successful, None otherwise
        """
        return self.authenticated_user