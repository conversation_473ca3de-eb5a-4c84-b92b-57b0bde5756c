@echo off
REM Project Management System - Run Script
REM This script activates the conda environment and runs the application

echo Starting Project Management System...

REM Activate the conda environment
call conda activate project_management

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to activate conda environment 'project_management'
    echo Please run install.bat first to set up the environment
    pause
    exit /b 1
)

REM Run the application
python main.py

REM Keep window open if there's an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Application exited with error code %ERRORLEVEL%
    pause
)