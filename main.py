#!/usr/bin/env python3
"""
Project Management Desktop Application
Main entry point for the application.

This application provides project management capabilities including:
- User management and authentication
- Project creation and management
- Vault management for storage locations
- Check-in/Check-out system (template)

Author: Augment Agent
Date: 2025-06-22
"""

import sys
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow
from logic.database import DatabaseManager
from logic.config import Config


def setup_logging():
    """Setup logging configuration for the application."""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main application entry point."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting Project Management Application")

    # Create QApplication instance
    app = QApplication(sys.argv)
    app.setApplicationName("Project Management System")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Project Management Corp")

    # Set application icon if available
    icon_path = project_root / "assets" / "app_icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    # Enable high DPI scaling
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    try:
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize_database()

        # Create and show main window
        main_window = MainWindow()
        main_window.show()

        logger.info("Application started successfully")

        # Run the application
        sys.exit(app.exec())

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()