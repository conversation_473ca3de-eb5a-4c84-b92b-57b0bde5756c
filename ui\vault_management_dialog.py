"""
Vault management dialog for Project Management Application.

This module provides vault management functionality including
add, edit, delete operations and storage path selection.

Author: Augment Agent
Date: 2025-06-22
"""

import logging
import os
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QMessageBox, QHeaderView, QAbstractItemView, QFrame,
    QLabel, QLineEdit, QFormLayout, QTextEdit, QFileDialog
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from logic.database import DatabaseManager


class VaultManagementDialog(QDialog):
    """Vault management dialog."""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        """
        Initialize the vault management dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager

        self.setup_ui()
        self.setup_connections()
        self.refresh_vaults()

    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("Vault Management")
        self.setMinimumSize(900, 600)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header
        header_label = QLabel("Vault Management")
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        main_layout.addWidget(header_label)

        # Info label
        info_label = QLabel("Vaults are storage locations where projects are stored. Each vault has a unique name and file system path.")
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # Vaults table
        self.vaults_table = QTableWidget()
        self.vaults_table.setColumnCount(5)
        self.vaults_table.setHorizontalHeaderLabels([
            "ID", "Name", "Path", "Created By", "Created"
        ])

        # Configure table
        header = self.vaults_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Name
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Path
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Created By

        self.vaults_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.vaults_table.setAlternatingRowColors(True)

        main_layout.addWidget(self.vaults_table)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.add_button = QPushButton("Add Vault")
        self.add_button.setMinimumHeight(35)
        self.add_button.setStyleSheet(self._get_button_style("#28a745"))

        self.edit_button = QPushButton("Edit Vault")
        self.edit_button.setMinimumHeight(35)
        self.edit_button.setStyleSheet(self._get_button_style("#007bff"))
        self.edit_button.setEnabled(False)

        self.delete_button = QPushButton("Delete Vault")
        self.delete_button.setMinimumHeight(35)
        self.delete_button.setStyleSheet(self._get_button_style("#dc3545"))
        self.delete_button.setEnabled(False)

        self.open_folder_button = QPushButton("Open Folder")
        self.open_folder_button.setMinimumHeight(35)
        self.open_folder_button.setStyleSheet(self._get_button_style("#17a2b8"))
        self.open_folder_button.setEnabled(False)

        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setMinimumHeight(35)
        self.refresh_button.setStyleSheet(self._get_button_style("#6c757d"))

        self.close_button = QPushButton("Close")
        self.close_button.setMinimumHeight(35)
        self.close_button.setStyleSheet(self._get_button_style("#6c757d"))

        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.open_folder_button)
        button_layout.addStretch()
        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def _get_button_style(self, bg_color: str, text_color: str = "#fff") -> str:
        """Get button stylesheet."""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {bg_color}dd;
            }}
            QPushButton:pressed {{
                background-color: {bg_color}bb;
            }}
            QPushButton:disabled {{
                background-color: #e9ecef;
                color: #6c757d;
            }}
        """

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.add_button.clicked.connect(self.add_vault)
        self.edit_button.clicked.connect(self.edit_vault)
        self.delete_button.clicked.connect(self.delete_vault)
        self.open_folder_button.clicked.connect(self.open_vault_folder)
        self.refresh_button.clicked.connect(self.refresh_vaults)
        self.close_button.clicked.connect(self.accept)

        # Enable/disable buttons based on selection
        self.vaults_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def refresh_vaults(self):
        """Refresh the vaults table."""
        try:
            vaults = self.db_manager.get_all_vaults()

            self.vaults_table.setRowCount(len(vaults))

            for row, vault in enumerate(vaults):
                # ID
                self.vaults_table.setItem(row, 0, QTableWidgetItem(str(vault['id'])))

                # Name
                self.vaults_table.setItem(row, 1, QTableWidgetItem(vault['name']))

                # Path
                path_item = QTableWidgetItem(vault['path'])
                path_item.setToolTip(vault['path'])  # Show full path in tooltip
                self.vaults_table.setItem(row, 2, path_item)

                # Created By
                created_by = vault['created_by_username'] or "Unknown"
                self.vaults_table.setItem(row, 3, QTableWidgetItem(created_by))

                # Created
                created = vault['created_at'] or ""
                if created:
                    # Format datetime for display
                    created = created.split('.')[0]  # Remove microseconds
                self.vaults_table.setItem(row, 4, QTableWidgetItem(created))

            self.logger.info(f"Loaded {len(vaults)} vaults")

        except Exception as e:
            self.logger.error(f"Failed to refresh vaults: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load vaults:\n{str(e)}")

    def on_selection_changed(self):
        """Handle table selection change."""
        has_selection = len(self.vaults_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.open_folder_button.setEnabled(has_selection)

    def get_selected_vault_id(self):
        """Get the ID of the currently selected vault."""
        current_row = self.vaults_table.currentRow()
        if current_row >= 0:
            id_item = self.vaults_table.item(current_row, 0)
            if id_item:
                return int(id_item.text())
        return None

    def get_selected_vault_path(self):
        """Get the path of the currently selected vault."""
        current_row = self.vaults_table.currentRow()
        if current_row >= 0:
            path_item = self.vaults_table.item(current_row, 2)
            if path_item:
                return path_item.text()
        return None

    def add_vault(self):
        """Add a new vault."""
        dialog = VaultEditDialog(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            self.refresh_vaults()

    def edit_vault(self):
        """Edit the selected vault."""
        vault_id = self.get_selected_vault_id()
        if vault_id is None:
            return

        # Get vault data
        vaults = self.db_manager.get_all_vaults()
        vault_data = next((v for v in vaults if v['id'] == vault_id), None)

        if vault_data:
            dialog = VaultEditDialog(self.db_manager, self, vault_data)
            if dialog.exec() == QDialog.Accepted:
                self.refresh_vaults()

    def delete_vault(self):
        """Delete the selected vault."""
        vault_id = self.get_selected_vault_id()
        if vault_id is None:
            return

        current_row = self.vaults_table.currentRow()
        vault_name = self.vaults_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "Delete Vault",
            f"Are you sure you want to delete vault '{vault_name}'?\n\n"
            "This action cannot be undone. Projects in this vault will become inaccessible.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager.delete_vault(vault_id):
                    QMessageBox.information(self, "Success", f"Vault '{vault_name}' deleted successfully.")
                    self.refresh_vaults()
                else:
                    QMessageBox.warning(self, "Error", "Failed to delete vault.")
            except Exception as e:
                self.logger.error(f"Failed to delete vault: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete vault:\n{str(e)}")

    def open_vault_folder(self):
        """Open the selected vault folder in file explorer."""
        vault_path = self.get_selected_vault_path()
        if vault_path and os.path.exists(vault_path):
            try:
                os.startfile(vault_path)  # Windows-specific
            except Exception as e:
                self.logger.error(f"Failed to open folder: {e}")
                QMessageBox.warning(self, "Error", f"Failed to open folder:\n{str(e)}")
        else:
            QMessageBox.warning(self, "Error", "Vault path does not exist or is not accessible.")


class VaultEditDialog(QDialog):
    """Dialog for adding/editing vaults."""

    def __init__(self, db_manager: DatabaseManager, parent=None, vault_data=None):
        """
        Initialize the vault edit dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
            vault_data: Vault data for editing (None for new vault)
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.vault_data = vault_data
        self.is_edit_mode = vault_data is not None

        self.setup_ui()
        self.setup_connections()

        if self.is_edit_mode:
            self.populate_fields()

    def setup_ui(self):
        """Setup the user interface."""
        title = "Edit Vault" if self.is_edit_mode else "Add Vault"
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header
        header_label = QLabel(title)
        header_font = QFont()
        header_font.setPointSize(14)
        header_font.setBold(True)
        header_label.setFont(header_font)
        main_layout.addWidget(header_label)

        # Form
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setContentsMargins(15, 15, 15, 15)
        form_layout.setSpacing(10)

        # Vault Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter vault name")
        form_layout.addRow("Vault Name:", self.name_edit)

        # Path selection
        path_layout = QHBoxLayout()
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("Select vault storage path")
        self.path_edit.setReadOnly(True)

        self.browse_button = QPushButton("Browse...")
        self.browse_button.setMaximumWidth(80)
        self.browse_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.browse_button)
        form_layout.addRow("Storage Path:", path_layout)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Enter vault description (optional)")
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)

        main_layout.addWidget(form_frame)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.save_button = QPushButton("Save")
        self.save_button.setMinimumHeight(35)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(35)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)

        main_layout.addLayout(button_layout)

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.browse_button.clicked.connect(self.browse_path)
        self.save_button.clicked.connect(self.save_vault)
        self.cancel_button.clicked.connect(self.reject)

    def populate_fields(self):
        """Populate fields with existing vault data."""
        if self.vault_data:
            self.name_edit.setText(self.vault_data['name'])
            self.path_edit.setText(self.vault_data['path'])
            self.description_edit.setPlainText(self.vault_data['description'] or "")

    def browse_path(self):
        """Browse for vault storage path."""
        current_path = self.path_edit.text() or os.path.expanduser("~")

        path = QFileDialog.getExistingDirectory(
            self, "Select Vault Storage Path", current_path
        )

        if path:
            self.path_edit.setText(path)

    def save_vault(self):
        """Save the vault data."""
        name = self.name_edit.text().strip()
        path = self.path_edit.text().strip()
        description = self.description_edit.toPlainText().strip()

        # Validate input
        if not name:
            QMessageBox.warning(self, "Validation Error", "Vault name is required.")
            return

        if not path:
            QMessageBox.warning(self, "Validation Error", "Storage path is required.")
            return

        # Validate path exists
        if not os.path.exists(path):
            reply = QMessageBox.question(
                self, "Path Not Found",
                f"The path '{path}' does not exist.\n\nDo you want to create it?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    os.makedirs(path, exist_ok=True)
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to create directory:\n{str(e)}")
                    return
            else:
                return

        try:
            if self.is_edit_mode:
                # Update existing vault
                success = self.db_manager.update_vault(
                    self.vault_data['id'],
                    name=name,
                    path=path,
                    description=description
                )
                action = "updated"
            else:
                # Create new vault (created_by would be set from current user in real app)
                success = self.db_manager.create_vault(
                    name, path, description, created_by=1  # Default to admin user
                )
                action = "created"

            if success:
                QMessageBox.information(self, "Success", f"Vault '{name}' {action} successfully.")
                self.accept()
            else:
                QMessageBox.warning(self, "Error", f"Failed to {action.replace('d', '')} vault. Name may already exist.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save vault:\n{str(e)}")