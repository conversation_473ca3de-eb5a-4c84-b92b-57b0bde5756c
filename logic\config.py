"""
Configuration module for Project Management Application.

This module handles application configuration and settings.

Author: Augment Agent
Date: 2025-06-22
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any


class Config:
    """Manages application configuration."""

    def __init__(self, config_path: str = None):
        """
        Initialize configuration manager.

        Args:
            config_path: Path to configuration file. If None, uses default.
        """
        self.logger = logging.getLogger(__name__)

        if config_path is None:
            project_root = Path(__file__).parent.parent
            data_dir = project_root / "data"
            data_dir.mkdir(exist_ok=True)
            config_path = data_dir / "config.json"

        self.config_path = Path(config_path)
        self.settings = self._load_default_settings()
        self.load()

    def _load_default_settings(self) -> Dict[str, Any]:
        """Load default application settings."""
        return {
            "app": {
                "name": "Project Management System",
                "version": "1.0.0",
                "window_width": 1200,
                "window_height": 800,
                "theme": "light"
            },
            "database": {
                "auto_backup": True,
                "backup_interval_hours": 24
            },
            "security": {
                "session_timeout_minutes": 60,
                "password_min_length": 6
            },
            "ui": {
                "show_tooltips": True,
                "confirm_deletions": True,
                "auto_save": True
            }
        }

    def load(self):
        """Load configuration from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    file_settings = json.load(f)
                    # Merge with defaults
                    self._merge_settings(self.settings, file_settings)
                self.logger.info(f"Configuration loaded from {self.config_path}")
            else:
                self.logger.info("No configuration file found, using defaults")
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")

    def save(self):
        """Save current configuration to file."""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.settings, f, indent=4)
            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")

    def _merge_settings(self, default: Dict, override: Dict):
        """Recursively merge override settings into default settings."""
        for key, value in override.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_settings(default[key], value)
            else:
                default[key] = value

    def get(self, key_path: str, default=None):
        """
        Get a configuration value using dot notation.

        Args:
            key_path: Dot-separated path to the setting (e.g., 'app.window_width')
            default: Default value if key not found

        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.settings

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key_path: str, value: Any):
        """
        Set a configuration value using dot notation.

        Args:
            key_path: Dot-separated path to the setting
            value: Value to set
        """
        keys = key_path.split('.')
        setting = self.settings

        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in setting:
                setting[key] = {}
            setting = setting[key]

        # Set the final value
        setting[keys[-1]] = value