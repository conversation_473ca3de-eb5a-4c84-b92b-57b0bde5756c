#!/usr/bin/env python3
"""
Test script to verify project structure and imports.
This script tests the imports without requiring PySide6.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing project structure and imports...")

    try:
        # Test logic imports
        print("✓ Testing logic package...")
        from logic.database import DatabaseManager
        from logic.config import Config
        print("✓ Logic package imports successful")

        # Test database initialization
        print("✓ Testing database initialization...")
        db_manager = DatabaseManager()
        print("✓ Database manager created successfully")

        # Test config initialization
        print("✓ Testing configuration...")
        config = Config()
        print("✓ Configuration manager created successfully")

        print("\n" + "="*50)
        print("✅ ALL TESTS PASSED!")
        print("✅ Project structure is correct")
        print("✅ Core modules are working")
        print("="*50)
        print("\nTo run the full application:")
        print("1. Install PySide6: pip install PySide6")
        print("2. Run: python main.py")
        print("\nOr use the automated installation:")
        print("1. Run: install.bat")
        print("2. Run: run.bat")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

if __name__ == "__main__":
    test_imports()