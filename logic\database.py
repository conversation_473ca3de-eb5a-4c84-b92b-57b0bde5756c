"""
Database management module for Project Management Application.

This module handles all database operations including:
- User management
- Project management
- Vault management
- Authentication

Author: Augment Agent
Date: 2025-06-22
"""

import sqlite3
import hashlib
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime


class DatabaseManager:
    """Manages SQLite database operations for the application."""

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the database manager.

        Args:
            db_path: Path to the SQLite database file. If None, uses default path.
        """
        self.logger = logging.getLogger(__name__)

        if db_path is None:
            # Use default database path in the data directory
            project_root = Path(__file__).parent.parent
            data_dir = project_root / "data"
            data_dir.mkdir(exist_ok=True)
            db_path = data_dir / "project_management.db"

        self.db_path = str(db_path)
        self.logger.info(f"Database path: {self.db_path}")

    def get_connection(self) -> sqlite3.Connection:
        """Get a database connection with row factory."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def initialize_database(self):
        """Initialize the database with required tables."""
        self.logger.info("Initializing database...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Create users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    is_admin BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)

            # Create vaults table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS vaults (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    path TEXT NOT NULL,
                    description TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Create projects table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    path TEXT NOT NULL,
                    description TEXT,
                    vault_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (vault_id) REFERENCES vaults (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Create user_groups table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create user_group_members table (many-to-many relationship)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_group_members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    group_id INTEGER,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (group_id) REFERENCES user_groups (id),
                    UNIQUE(user_id, group_id)
                )
            """)

            # Create project_permissions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS project_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    group_id INTEGER,
                    can_checkout BOOLEAN DEFAULT 0,
                    can_checkin BOOLEAN DEFAULT 0,
                    can_read BOOLEAN DEFAULT 1,
                    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    FOREIGN KEY (group_id) REFERENCES user_groups (id),
                    UNIQUE(project_id, group_id)
                )
            """)

            conn.commit()
            self.logger.info("Database initialized successfully")

            # Create default admin user if no users exist
            self._create_default_admin_user()

    def _create_default_admin_user(self):
        """Create a default admin user if no users exist."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]

            if user_count == 0:
                self.logger.info("Creating default admin user...")
                password_hash = self._hash_password("admin123")
                cursor.execute("""
                    INSERT INTO users (username, password_hash, full_name, email, is_admin)
                    VALUES (?, ?, ?, ?, ?)
                """, ("admin", password_hash, "Administrator", "<EMAIL>", True))
                conn.commit()
                self.logger.info("Default admin user created (username: admin, password: admin123)")

    @staticmethod
    def _hash_password(password: str) -> str:
        """Hash a password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()

    # User Management Methods
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """
        Authenticate a user with username and password.

        Args:
            username: The username
            password: The plain text password

        Returns:
            User dictionary if authentication successful, None otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            password_hash = self._hash_password(password)

            cursor.execute("""
                SELECT id, username, full_name, email, is_admin, is_active
                FROM users
                WHERE username = ? AND password_hash = ? AND is_active = 1
            """, (username, password_hash))

            user = cursor.fetchone()
            if user:
                # Update last login
                cursor.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                """, (user['id'],))
                conn.commit()
                return dict(user)
            return None

    def create_user(self, username: str, password: str, full_name: str,
                   email: str = "", is_admin: bool = False) -> bool:
        """
        Create a new user.

        Args:
            username: Unique username
            password: Plain text password
            full_name: User's full name
            email: User's email address
            is_admin: Whether user has admin privileges

        Returns:
            True if user created successfully, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                password_hash = self._hash_password(password)

                cursor.execute("""
                    INSERT INTO users (username, password_hash, full_name, email, is_admin)
                    VALUES (?, ?, ?, ?, ?)
                """, (username, password_hash, full_name, email, is_admin))

                conn.commit()
                self.logger.info(f"User '{username}' created successfully")
                return True
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to create user '{username}': {e}")
            return False

    def get_all_users(self) -> List[Dict]:
        """Get all active users."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, username, full_name, email, is_admin, created_at, last_login
                FROM users
                WHERE is_active = 1
                ORDER BY username
            """)
            return [dict(row) for row in cursor.fetchall()]

    def update_user(self, user_id: int, **kwargs) -> bool:
        """
        Update user information.

        Args:
            user_id: User ID to update
            **kwargs: Fields to update (username, full_name, email, is_admin)

        Returns:
            True if update successful, False otherwise
        """
        if not kwargs:
            return False

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Build dynamic update query
                set_clauses = []
                values = []

                for field, value in kwargs.items():
                    if field in ['username', 'full_name', 'email', 'is_admin']:
                        set_clauses.append(f"{field} = ?")
                        values.append(value)

                if not set_clauses:
                    return False

                values.append(user_id)
                query = f"UPDATE users SET {', '.join(set_clauses)} WHERE id = ?"

                cursor.execute(query, values)
                conn.commit()

                return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to update user {user_id}: {e}")
            return False

    def delete_user(self, user_id: int) -> bool:
        """
        Soft delete a user (set is_active to False).

        Args:
            user_id: User ID to delete

        Returns:
            True if deletion successful, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE users SET is_active = 0 WHERE id = ?", (user_id,))
            conn.commit()
            return cursor.rowcount > 0

    def change_password(self, user_id: int, new_password: str) -> bool:
        """
        Change a user's password.

        Args:
            user_id: User ID
            new_password: New plain text password

        Returns:
            True if password changed successfully, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            password_hash = self._hash_password(new_password)
            cursor.execute("UPDATE users SET password_hash = ? WHERE id = ?",
                         (password_hash, user_id))
            conn.commit()
            return cursor.rowcount > 0

    # Vault Management Methods
    def create_vault(self, name: str, path: str, description: str = "",
                    created_by: int = None) -> bool:
        """
        Create a new vault.

        Args:
            name: Vault name
            path: Storage path for the vault
            description: Optional description
            created_by: User ID who created the vault

        Returns:
            True if vault created successfully, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO vaults (name, path, description, created_by)
                    VALUES (?, ?, ?, ?)
                """, (name, path, description, created_by))
                conn.commit()
                self.logger.info(f"Vault '{name}' created successfully")
                return True
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to create vault '{name}': {e}")
            return False

    def get_all_vaults(self) -> List[Dict]:
        """Get all active vaults."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT v.id, v.name, v.path, v.description, v.created_at,
                       u.username as created_by_username
                FROM vaults v
                LEFT JOIN users u ON v.created_by = u.id
                WHERE v.is_active = 1
                ORDER BY v.name
            """)
            return [dict(row) for row in cursor.fetchall()]

    def update_vault(self, vault_id: int, **kwargs) -> bool:
        """
        Update vault information.

        Args:
            vault_id: Vault ID to update
            **kwargs: Fields to update (name, path, description)

        Returns:
            True if update successful, False otherwise
        """
        if not kwargs:
            return False

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                set_clauses = []
                values = []

                for field, value in kwargs.items():
                    if field in ['name', 'path', 'description']:
                        set_clauses.append(f"{field} = ?")
                        values.append(value)

                if not set_clauses:
                    return False

                values.append(vault_id)
                query = f"UPDATE vaults SET {', '.join(set_clauses)} WHERE id = ?"

                cursor.execute(query, values)
                conn.commit()

                return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to update vault {vault_id}: {e}")
            return False

    def delete_vault(self, vault_id: int) -> bool:
        """
        Soft delete a vault (set is_active to False).

        Args:
            vault_id: Vault ID to delete

        Returns:
            True if deletion successful, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE vaults SET is_active = 0 WHERE id = ?", (vault_id,))
            conn.commit()
            return cursor.rowcount > 0

    # Project Management Methods
    def create_project(self, name: str, path: str, vault_id: int,
                      description: str = "", created_by: int = None) -> bool:
        """
        Create a new project.

        Args:
            name: Project name
            path: Project path
            vault_id: ID of the vault this project belongs to
            description: Optional description
            created_by: User ID who created the project

        Returns:
            True if project created successfully, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO projects (name, path, vault_id, description, created_by)
                    VALUES (?, ?, ?, ?, ?)
                """, (name, path, vault_id, description, created_by))
                conn.commit()
                self.logger.info(f"Project '{name}' created successfully")
                return True
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to create project '{name}': {e}")
            return False

    def get_all_projects(self) -> List[Dict]:
        """Get all active projects with vault and creator information."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT p.id, p.name, p.path, p.description, p.created_at,
                       v.name as vault_name, u.username as created_by_username
                FROM projects p
                LEFT JOIN vaults v ON p.vault_id = v.id
                LEFT JOIN users u ON p.created_by = u.id
                WHERE p.is_active = 1
                ORDER BY p.name
            """)
            return [dict(row) for row in cursor.fetchall()]

    def update_project(self, project_id: int, **kwargs) -> bool:
        """
        Update project information.

        Args:
            project_id: Project ID to update
            **kwargs: Fields to update (name, path, description, vault_id)

        Returns:
            True if update successful, False otherwise
        """
        if not kwargs:
            return False

        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                set_clauses = []
                values = []

                for field, value in kwargs.items():
                    if field in ['name', 'path', 'description', 'vault_id']:
                        set_clauses.append(f"{field} = ?")
                        values.append(value)

                if not set_clauses:
                    return False

                values.append(project_id)
                query = f"UPDATE projects SET {', '.join(set_clauses)} WHERE id = ?"

                cursor.execute(query, values)
                conn.commit()

                return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            self.logger.error(f"Failed to update project {project_id}: {e}")
            return False

    def delete_project(self, project_id: int) -> bool:
        """
        Soft delete a project (set is_active to False).

        Args:
            project_id: Project ID to delete

        Returns:
            True if deletion successful, False otherwise
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE projects SET is_active = 0 WHERE id = ?", (project_id,))
            conn.commit()
            return cursor.rowcount > 0
