"""
User management dialog for Project Management Application.

This module provides user management functionality including
add, edit, delete, and password change operations.

Author: Augment Agent
Date: 2025-06-22
"""

import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QMessageBox, QHeaderView, QAbstractItemView, QFrame,
    QLabel, QLineEdit, QFormLayout, QCheckBox, QInputDialog
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from logic.database import DatabaseManager


class UserManagementDialog(QDialog):
    """User management dialog for admin users."""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        """
        Initialize the user management dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.logger = logging.getLogger(__name__)
        self.db_manager = db_manager

        self.setup_ui()
        self.setup_connections()
        self.refresh_users()

    def setup_ui(self):
        """Setup the user interface."""
        self.setWindowTitle("User Management")
        self.setMinimumSize(800, 600)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header
        header_label = QLabel("User Management")
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        main_layout.addWidget(header_label)

        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "ID", "Username", "Full Name", "Email", "Admin", "Created"
        ])

        # Configure table
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Username
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Full Name
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # Email
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Admin

        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setAlternatingRowColors(True)

        main_layout.addWidget(self.users_table)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.add_button = QPushButton("Add User")
        self.add_button.setMinimumHeight(35)
        self.add_button.setStyleSheet(self._get_button_style("#28a745"))

        self.edit_button = QPushButton("Edit User")
        self.edit_button.setMinimumHeight(35)
        self.edit_button.setStyleSheet(self._get_button_style("#007bff"))
        self.edit_button.setEnabled(False)

        self.delete_button = QPushButton("Delete User")
        self.delete_button.setMinimumHeight(35)
        self.delete_button.setStyleSheet(self._get_button_style("#dc3545"))
        self.delete_button.setEnabled(False)

        self.change_password_button = QPushButton("Change Password")
        self.change_password_button.setMinimumHeight(35)
        self.change_password_button.setStyleSheet(self._get_button_style("#ffc107", "#000"))
        self.change_password_button.setEnabled(False)

        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setMinimumHeight(35)
        self.refresh_button.setStyleSheet(self._get_button_style("#6c757d"))

        self.close_button = QPushButton("Close")
        self.close_button.setMinimumHeight(35)
        self.close_button.setStyleSheet(self._get_button_style("#6c757d"))

        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.change_password_button)
        button_layout.addStretch()
        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def _get_button_style(self, bg_color: str, text_color: str = "#fff") -> str:
        """Get button stylesheet."""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {bg_color}dd;
            }}
            QPushButton:pressed {{
                background-color: {bg_color}bb;
            }}
            QPushButton:disabled {{
                background-color: #e9ecef;
                color: #6c757d;
            }}
        """

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.add_button.clicked.connect(self.add_user)
        self.edit_button.clicked.connect(self.edit_user)
        self.delete_button.clicked.connect(self.delete_user)
        self.change_password_button.clicked.connect(self.change_password)
        self.refresh_button.clicked.connect(self.refresh_users)
        self.close_button.clicked.connect(self.accept)

        # Enable/disable buttons based on selection
        self.users_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def refresh_users(self):
        """Refresh the users table."""
        try:
            users = self.db_manager.get_all_users()

            self.users_table.setRowCount(len(users))

            for row, user in enumerate(users):
                # ID
                self.users_table.setItem(row, 0, QTableWidgetItem(str(user['id'])))

                # Username
                self.users_table.setItem(row, 1, QTableWidgetItem(user['username']))

                # Full Name
                self.users_table.setItem(row, 2, QTableWidgetItem(user['full_name']))

                # Email
                email = user['email'] or ""
                self.users_table.setItem(row, 3, QTableWidgetItem(email))

                # Admin
                admin_text = "Yes" if user['is_admin'] else "No"
                admin_item = QTableWidgetItem(admin_text)
                admin_item.setTextAlignment(Qt.AlignCenter)
                self.users_table.setItem(row, 4, admin_item)

                # Created
                created = user['created_at'] or ""
                if created:
                    # Format datetime for display
                    created = created.split('.')[0]  # Remove microseconds
                self.users_table.setItem(row, 5, QTableWidgetItem(created))

            self.logger.info(f"Loaded {len(users)} users")

        except Exception as e:
            self.logger.error(f"Failed to refresh users: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load users:\n{str(e)}")

    def on_selection_changed(self):
        """Handle table selection change."""
        has_selection = len(self.users_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.change_password_button.setEnabled(has_selection)

    def get_selected_user_id(self):
        """Get the ID of the currently selected user."""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            id_item = self.users_table.item(current_row, 0)
            if id_item:
                return int(id_item.text())
        return None

    def add_user(self):
        """Add a new user."""
        dialog = UserEditDialog(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            self.refresh_users()

    def edit_user(self):
        """Edit the selected user."""
        user_id = self.get_selected_user_id()
        if user_id is None:
            return

        # Get user data
        users = self.db_manager.get_all_users()
        user_data = next((u for u in users if u['id'] == user_id), None)

        if user_data:
            dialog = UserEditDialog(self.db_manager, self, user_data)
            if dialog.exec() == QDialog.Accepted:
                self.refresh_users()

    def delete_user(self):
        """Delete the selected user."""
        user_id = self.get_selected_user_id()
        if user_id is None:
            return

        current_row = self.users_table.currentRow()
        username = self.users_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, "Delete User",
            f"Are you sure you want to delete user '{username}'?\n\n"
            "This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager.delete_user(user_id):
                    QMessageBox.information(self, "Success", f"User '{username}' deleted successfully.")
                    self.refresh_users()
                else:
                    QMessageBox.warning(self, "Error", "Failed to delete user.")
            except Exception as e:
                self.logger.error(f"Failed to delete user: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete user:\n{str(e)}")

    def change_password(self):
        """Change password for the selected user."""
        user_id = self.get_selected_user_id()
        if user_id is None:
            return

        current_row = self.users_table.currentRow()
        username = self.users_table.item(current_row, 1).text()

        new_password, ok = QInputDialog.getText(
            self, "Change Password",
            f"Enter new password for '{username}':",
            QLineEdit.Password
        )

        if ok and new_password:
            if len(new_password) < 6:
                QMessageBox.warning(self, "Invalid Password", "Password must be at least 6 characters long.")
                return

            try:
                if self.db_manager.change_password(user_id, new_password):
                    QMessageBox.information(self, "Success", f"Password changed successfully for '{username}'.")
                else:
                    QMessageBox.warning(self, "Error", "Failed to change password.")
            except Exception as e:
                self.logger.error(f"Failed to change password: {e}")
                QMessageBox.critical(self, "Error", f"Failed to change password:\n{str(e)}")


class UserEditDialog(QDialog):
    """Dialog for adding/editing users."""

    def __init__(self, db_manager: DatabaseManager, parent=None, user_data=None):
        """
        Initialize the user edit dialog.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
            user_data: User data for editing (None for new user)
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.user_data = user_data
        self.is_edit_mode = user_data is not None

        self.setup_ui()
        self.setup_connections()

        if self.is_edit_mode:
            self.populate_fields()

    def setup_ui(self):
        """Setup the user interface."""
        title = "Edit User" if self.is_edit_mode else "Add User"
        self.setWindowTitle(title)
        self.setFixedSize(400, 350)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Header
        header_label = QLabel(title)
        header_font = QFont()
        header_font.setPointSize(14)
        header_font.setBold(True)
        header_label.setFont(header_font)
        main_layout.addWidget(header_label)

        # Form
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_layout = QFormLayout(form_frame)
        form_layout.setContentsMargins(15, 15, 15, 15)
        form_layout.setSpacing(10)

        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter username")
        form_layout.addRow("Username:", self.username_edit)

        # Full Name
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText("Enter full name")
        form_layout.addRow("Full Name:", self.full_name_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Enter email address")
        form_layout.addRow("Email:", self.email_edit)

        # Password (only for new users)
        if not self.is_edit_mode:
            self.password_edit = QLineEdit()
            self.password_edit.setPlaceholderText("Enter password")
            self.password_edit.setEchoMode(QLineEdit.Password)
            form_layout.addRow("Password:", self.password_edit)

        # Admin checkbox
        self.admin_checkbox = QCheckBox("Administrator privileges")
        form_layout.addRow("", self.admin_checkbox)

        main_layout.addWidget(form_frame)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.save_button = QPushButton("Save")
        self.save_button.setMinimumHeight(35)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setMinimumHeight(35)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)

        main_layout.addLayout(button_layout)

    def setup_connections(self):
        """Setup signal-slot connections."""
        self.save_button.clicked.connect(self.save_user)
        self.cancel_button.clicked.connect(self.reject)

    def populate_fields(self):
        """Populate fields with existing user data."""
        if self.user_data:
            self.username_edit.setText(self.user_data['username'])
            self.full_name_edit.setText(self.user_data['full_name'])
            self.email_edit.setText(self.user_data['email'] or "")
            self.admin_checkbox.setChecked(self.user_data['is_admin'])

    def save_user(self):
        """Save the user data."""
        username = self.username_edit.text().strip()
        full_name = self.full_name_edit.text().strip()
        email = self.email_edit.text().strip()
        is_admin = self.admin_checkbox.isChecked()

        # Validate input
        if not username:
            QMessageBox.warning(self, "Validation Error", "Username is required.")
            return

        if not full_name:
            QMessageBox.warning(self, "Validation Error", "Full name is required.")
            return

        if not self.is_edit_mode:
            password = self.password_edit.text()
            if not password:
                QMessageBox.warning(self, "Validation Error", "Password is required.")
                return
            if len(password) < 6:
                QMessageBox.warning(self, "Validation Error", "Password must be at least 6 characters long.")
                return

        try:
            if self.is_edit_mode:
                # Update existing user
                success = self.db_manager.update_user(
                    self.user_data['id'],
                    username=username,
                    full_name=full_name,
                    email=email,
                    is_admin=is_admin
                )
                action = "updated"
            else:
                # Create new user
                success = self.db_manager.create_user(
                    username, password, full_name, email, is_admin
                )
                action = "created"

            if success:
                QMessageBox.information(self, "Success", f"User '{username}' {action} successfully.")
                self.accept()
            else:
                QMessageBox.warning(self, "Error", f"Failed to {action.replace('d', '')} user. Username may already exist.")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save user:\n{str(e)}")