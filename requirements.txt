# Project Management System Requirements
#
# To create a conda environment with Python 3.12:
# conda create -n project_management python=3.12
# conda activate project_management
# pip install -r requirements.txt
#
# Or use the provided install.bat file for automated installation

# Core GUI Framework
PySide6>=6.6.0

# Additional dependencies (automatically installed with PySide6)
# - Qt6 bindings
# - Qt<PERSON>ore, QtWidgets, QtGui modules

# Standard library modules used (no installation required):
# - sqlite3 (database)
# - hashlib (password hashing)
# - logging (application logging)
# - pathlib (file path handling)
# - json (configuration files)
# - datetime (timestamps)
# - os (operating system interface)
# - typing (type hints)